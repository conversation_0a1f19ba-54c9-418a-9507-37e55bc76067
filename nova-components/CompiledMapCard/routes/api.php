<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Models\Map;
use App\Models\MapPoint;
use App\Models\MapArea;

/*
|--------------------------------------------------------------------------
| Card API Routes
|--------------------------------------------------------------------------
|
| Here is where you may register API routes for your card. These routes
| are loaded by the ServiceProvider of your card. You're free to add
| as many additional routes to this file as your card may require.
|
*/

// API endpoint to get map points for a specific map
Route::get('/map-points/{mapId}', function (Request $request, $mapId) {
    try {
        $map = Map::findOrFail($mapId);
        $points = $map->mapPoints;

        return response()->json([
            'success' => true,
            'data' => $points->map(function ($point) {
                return [
                    'id' => $point->id,
                    'name' => $point->name,
                    'description' => $point->description,
                    'location' => $point->location,
                    'created_at' => $point->created_at,
                    'updated_at' => $point->updated_at,
                ];
            }),
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
        ], 500);
    }
});

// API endpoint to get map areas for a specific map
Route::get('/map-areas/{mapId}', function (Request $request, $mapId) {
    try {
        $map = Map::findOrFail($mapId);
        $areas = $map->mapAreas;

        return response()->json([
            'success' => true,
            'data' => $areas->map(function ($area) {
                return [
                    'id' => $area->id,
                    'name' => $area->name,
                    'description' => $area->description,
                    'area' => $area->area,
                    'style' => [
                        'fillColor' => $area->fill_color,
                        'fillOpacity' => $area->fill_opacity,
                        'color' => $area->stroke_color,
                        'weight' => $area->stroke_width,
                        'opacity' => $area->stroke_opacity,
                        'dashArray' => $area->dash_pattern,
                    ],
                    'created_at' => $area->created_at,
                    'updated_at' => $area->updated_at,
                ];
            }),
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
        ], 500);
    }
});
