<?php

namespace App\Traits;

use <PERSON><PERSON>\Hashids\Facades\Hashids;

trait HasObfuscatedId
{
    /**
     * Get the obfuscated ID for the model.
     *
     * @param string $connection The HashIds connection to use
     * @return string
     */
    public function getObfuscatedId(string $connection = 'main'): string
    {
        return Hashids::connection($connection)->encode($this->getKey());
    }

    /**
     * Find a model by its obfuscated ID.
     *
     * @param string $obfuscatedId
     * @param string $connection The HashIds connection to use
     * @return static|null
     */
    public static function findByObfuscatedId(string $obfuscatedId, string $connection = 'main')
    {
        $ids = Hashids::connection($connection)->decode($obfuscatedId);
        
        if (empty($ids)) {
            return null;
        }
        
        return static::find($ids[0]);
    }

    /**
     * Find a model by its obfuscated ID or throw an exception.
     *
     * @param string $obfuscatedId
     * @param string $connection The HashIds connection to use
     * @return static
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     */
    public static function findByObfuscatedIdOrFail(string $obfuscatedId, string $connection = 'main')
    {
        $model = static::findByObfuscatedId($obfuscatedId, $connection);
        
        if (is_null($model)) {
            throw (new \Illuminate\Database\Eloquent\ModelNotFoundException)->setModel(static::class);
        }
        
        return $model;
    }
}
