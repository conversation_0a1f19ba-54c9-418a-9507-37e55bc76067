<?php

namespace App\Policies;

use App\Models\VantagePoint;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class VantagePointPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, VantagePoint $vantagePoint): bool
    {
        switch ($user->role->name) {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $vantagePoint->projectIteration->project->client)
                {
                    return true;   
                    break;
                }                 

            case 'Project Administrator':
                if($user->target_project_id == $vantagePoint->projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Project Viewer':
                if($user->target_project_id == $vantagePoint->projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Administrator':
                if($user->target_iteration_id == $vantagePoint->projectIteration->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Viewer':
                if($user->target_iteration_id == $vantagePoint->projectIteration->id)
                {
                    return true;
                    break;
                }
                        
            default:
                   return false;
                   break;
        }
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':            
                return true;
                break;

            case 'Client Administrator':            
                return true;
                break;

            case 'Project Administrator':            
                return true;
                break;

            case 'Iteration Administrator':
                return true;
                break;

            default:
                return false;
                break;
        }
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, VantagePoint $vantagePoint): bool
    {
        switch ($user->role->name) {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $vantagePoint->projectIteration->project->client)
                {
                    return true;   
                    break;
                }                 

            case 'Project Administrator':
                if($user->target_project_id == $vantagePoint->projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Administrator':
                if($user->target_iteration_id == $vantagePoint->projectIteration->id)
                {
                    return true;
                    break;
                }
                        
            default:
                   return false;
                   break;
        }
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, VantagePoint $vantagePoint): bool
    {
        switch ($user->role->name) {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $vantagePoint->projectIteration->project->client)
                {
                    return true;   
                    break;
                }                 

            case 'Project Administrator':
                if($user->target_project_id == $vantagePoint->projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Administrator':
                if($user->target_iteration_id == $vantagePoint->projectIteration->id)
                {
                    return true;
                    break;
                }
                        
            default:
                   return false;
                   break;
        }
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, VantagePoint $vantagePoint): bool
    {
        switch ($user->role->name) {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $vantagePoint->projectIteration->project->client)
                {
                    return true;   
                    break;
                }                 

            case 'Project Administrator':
                if($user->target_project_id == $vantagePoint->projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Administrator':
                if($user->target_iteration_id == $vantagePoint->projectIteration->id)
                {
                    return true;
                    break;
                }
                        
            default:
                   return false;
                   break;
        }
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, VantagePoint $vantagePoint): bool
    {
        switch ($user->role->name) {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $vantagePoint->projectIteration->project->client)
                {
                    return true;   
                    break;
                }                 

            case 'Project Administrator':
                if($user->target_project_id == $vantagePoint->projectIteration->project->id)
                {
                    return false;
                    break;
                }

            case 'Iteration Administrator':
                if($user->target_iteration_id == $vantagePoint->projectIteration->id)
                {
                    return false;
                    break;
                }
                        
            default:
                   return false;
                   break;
        }
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, VantagePoint $vantagePoint): bool
    {
        return false;
    }
}
