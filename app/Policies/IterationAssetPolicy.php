<?php

namespace App\Policies;

use App\Models\IterationAsset;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class IterationAssetPolicy
{
    /**
     * Determine whether the user can view any models.
     * 
     */
    public function viewAny(User $user): bool
    {
        switch ($user->role->name)
        {
            case 'Global Administrator':
                return true;
                break;

           default:
                return false;
                break;
        }
    }

    /**
     * Determine whether a user can write the vertical offset.
     */
    public function updateVerticalOffset(User $user, IterationAsset $iterationAsset)
    {
        return true;
    }
}