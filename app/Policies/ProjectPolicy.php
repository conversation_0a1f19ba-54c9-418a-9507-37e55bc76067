<?php

namespace App\Policies;

use App\Models\Project;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ProjectPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Project $project): bool
    {
        switch ($user->role->name) {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $project->client)
                {
                    return true;
                    break;
                }
                        
            default:
                if($user->target_project_id == $project->id)
                {
                   return true;
                   break;
                }
                return false;                
        }
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        switch ($user->role->name) {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                return true;
                break;
                        
            default:                
                return false;  
                break;              
        }
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Project $project): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                return true;
                break;

            case 'Project Administrator':
                if($user->target_project_id == $project->id)
                {
                   return true;
                   break;
                }
                        
            default:               
                return false;            
        }
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Project $project): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                return true;
                break;
                        
            default:                
                return false;  
                break;              
        }
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Project $project): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Project $project): bool
    {
        return false;
    }
}
