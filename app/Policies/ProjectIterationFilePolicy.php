<?php

namespace App\Policies;

use App\Models\ProjectIterationFile;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ProjectIterationFilePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ProjectIterationFile $projectIterationFile): bool
    {
        switch ($user->role->name) {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $projectIterationFile->projectIteration->project->client)
                {
                    return true;   
                    break;
                }                 

            case 'Project Administrator':
                if($user->target_project_id == $projectIterationFile->projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Project Viewer':
                if($user->target_project_id == $projectIterationFile->projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Administrator':
                if($user->target_iteration_id == $projectIterationFile->projectIteration->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Viewer':
                if($user->target_iteration_id == $projectIterationFile->projectIteration->id)
                {
                    return true;
                    break;
                }
                        
            default:
                   return false;
                   break;
        }
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':            
                return true;
                break;

            case 'Client Administrator':            
                    return true;
                    break;

            case 'Project Administrator':            
                return true;
                break;

            default:
                return false;
                break;
        }
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ProjectIterationFile $projectIterationFile): bool
    {
        switch ($user->role->name) {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $projectIterationFile->projectIteration->project->client)
                {
                    return true;   
                    break;
                }                 

            case 'Project Administrator':
                if($user->target_project_id == $projectIterationFile->projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Project Viewer':
                if($user->target_project_id == $projectIterationFile->projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Administrator':
                if($user->target_iteration_id == $projectIterationFile->projectIteration->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Viewer':
                if($user->target_iteration_id == $projectIterationFile->projectIteration->id)
                {
                    return true;
                    break;
                }
                        
            default:
                   return false;
                   break;
        }
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ProjectIterationFile $projectIterationFile): bool
    {
        switch ($user->role->name) {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $projectIterationFile->projectIteration->project->client)
                {
                    return true;   
                    break;
                }                 

            case 'Project Administrator':
                if($user->target_project_id == $projectIterationFile->projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Project Viewer':
                if($user->target_project_id == $projectIterationFile->projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Administrator':
                if($user->target_iteration_id == $projectIterationFile->projectIteration->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Viewer':
                if($user->target_iteration_id == $projectIterationFile->projectIteration->id)
                {
                    return true;
                    break;
                }
                        
            default:
                   return false;
                   break;
        }
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, ProjectIterationFile $projectIterationFile): bool
    {
        switch ($user->role->name) {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $projectIterationFile->projectIteration->project->client)
                {
                    return true;   
                    break;
                }                 

            case 'Project Administrator':
                if($user->target_project_id == $projectIterationFile->projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Project Viewer':
                if($user->target_project_id == $projectIterationFile->projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Administrator':
                if($user->target_iteration_id == $projectIterationFile->projectIteration->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Viewer':
                if($user->target_iteration_id == $projectIterationFile->projectIteration->id)
                {
                    return true;
                    break;
                }
                        
            default:
                   return false;
                   break;
        }
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, ProjectIterationFile $projectIterationFile): bool
    {
        switch ($user->role->name) {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $projectIterationFile->projectIteration->project->client)
                {
                    return true;   
                    break;
                }                 

            case 'Project Administrator':
                if($user->target_project_id == $projectIterationFile->projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Project Viewer':
                if($user->target_project_id == $projectIterationFile->projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Administrator':
                if($user->target_iteration_id == $projectIterationFile->projectIteration->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Viewer':
                if($user->target_iteration_id == $projectIterationFile->projectIteration->id)
                {
                    return true;
                    break;
                }
                        
            default:
                   return false;
                   break;
        }
    }

    /**
     * Determine whether the user can detach an ClientModel the model.
     */
    public function detachClientModel(User $user, ProjectIterationFile $projectIterationFile): bool
    {
        switch ($user->role->name)
        {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $projectIterationFile->projectIteration->project->client)
                {
                    return true;   
                    break;
                }                 

            case 'Project Administrator':
                if($user->target_project_id == $projectIterationFile->projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Project Viewer':
                if($user->target_project_id == $projectIterationFile->projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Administrator':
                if($user->target_iteration_id == $projectIterationFile->projectIteration->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Viewer':
                if($user->target_iteration_id == $projectIterationFile->projectIteration->id)
                {
                    return true;
                    break;
                }
                        
            default:
                   return false;
                   break;
        }
    }

    public function replicate(User $user, ProjectIterationFile $projectIterationFile): bool
    {
        return false;
    }
}
