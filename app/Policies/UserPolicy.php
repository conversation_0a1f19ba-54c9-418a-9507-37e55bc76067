<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\Response;
use League\CommonMark\Node\Query\OrExpr;

class UserPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                return true;
                break;

            case 'Project Administrator':
                return true;
                break;
                        
            default:
                return false;                
        }
    }


    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, User $model): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $model->client)
                {
                    return true;
                    break;
                }

            case 'Project Administrator':
                if($user->client == $model->client)
                {
                    return true;
                    break;
                }
                        
            default:
                return false;                
        }
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                return true;
                break;

            case 'Project Administrator':
                return true;
                break;
                        
            default:
                return false;                
        }
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, User $model): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $model->client)
                {
                    return true;
                    break;
                }

            case 'Project Administrator':
                if($user->client == $model->client)
                {
                    return true;
                    break;
                }
                        
            default:
                return false;                
        }
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, User $model): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $model->client)
                {
                    return true;
                    break;
                }
                        
            default:
                return false;                
        }
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, User $model): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $model->client)
                {
                    return true;
                    break;
                }

            case 'Project Administrator':
                if($user->client == $model->client)
                {
                    return true;
                    break;
                }
                        
            default:
                return false;                
        }
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, User $model): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $model->client)
                {
                    return true;
                    break;
                }

            case 'Project Administrator':
                if($user->client == $model->client)
                {
                    return true;
                    break;
                }
                        
            default:
                return false;                
        }
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, User $model): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $model->client)
                {
                    return true;
                    break;
                }

            case 'Project Administrator':
                if($user->client == $model->client)
                {
                    return true;
                    break;
                }
                        
            default:
                return false;                
        }
    }
}
