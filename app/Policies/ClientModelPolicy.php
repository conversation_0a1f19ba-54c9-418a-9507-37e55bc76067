<?php

namespace App\Policies;

use App\Models\ClientModel;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ClientModelPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ClientModel $clientModel): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $clientModel->client)
                    return true;
                else
                    return false;
                
                break;

            case 'Project Administrator':
                if($user->client == $clientModel->client)
                    return true;
                else
                    return false;
                
                break;
                    
            case 'Iteration Administrator':
                if($user->client == $clientModel->client)
                    return true;
                else
                    return false;
                
                break;
            
            default:
                return false;
                break;
        }
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                return true;
                break;

            case 'Project Administrator':
                return true;
                break;
                    
            case 'Iteration Administrator':
                return true;
                break;   
            
            default:
                return false;
                break;
        }
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ClientModel $clientModel): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $clientModel->client)
                    return true;
                else
                    return false;
                
                break;
            
            default:
                return false;
                break;
        }
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ClientModel $clientModel): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $clientModel->client)
                    return true;
                else
                    return false;
                
                break;
            
            default:
                return false;
                break;
        }
    }
        
     /**
     * Determine whether the user can detach an Iteration the model.
     */
    public function detachProjectIteration(User $user, ClientModel $clientModel): bool
    {
        return false;
    }

    public function replicate(User $user, ClientModel $clientModel): bool
    {
        return false;
    }
}