<?php

namespace App\Policies;

use App\Models\ProjectIteration;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ProjectIterationPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ProjectIteration $projectIteration): bool
    {
        switch ($user->role->name) {
            case 'Global Administrator':
                return true;
                break;

            case 'Client Administrator':
                if($user->client == $projectIteration->project->client)
                {
                    return true;   
                    break;
                }                 

            case 'Project Administrator':
                if($user->target_project_id == $projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Project Viewer':
                if($user->target_project_id == $projectIteration->project->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Administrator':
                if($user->target_iteration_id == $projectIteration->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Viewer':
                if($user->target_iteration_id == $projectIteration->id)
                {
                    return true;
                    break;
                }
                        
            default:
                   return false;
                   break;
        }
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':            
                return true;
                break;

            case 'Client Administrator':            
                    return true;
                    break;

            case 'Project Administrator':            
                return true;
                break;

            default:
                return false;
                break;
        }
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ProjectIteration $projectIteration): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':            
                return true;
                break;

            case 'Client Administrator':            
                if($user->client == $projectIteration->project->client)
                {
                    return true;
                    break;
                }

            case 'Project Administrator':            
                if($user->target_project_id == $projectIteration->project_id)
                {
                    return true;
                    break;
                }

            case 'Project Viewer':
                if($user->target_project_id == $projectIteration->project_id)
                {
                    return false;
                    break;
                }

            case 'Iteration Administrator':
                if($user->target_iteration_id == $projectIteration->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Viewer':
                if($user->target_iteration_id == $projectIteration->id)
                {
                    return false;
                    break;
                }
                        
            default:
                    return false;
                    break;
        }
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ProjectIteration $projectIteration): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':            
                return true;
                break;

            case 'Client Administrator':            
                if($user->client == $projectIteration->project->client)
                {
                    return true;
                    break;
                }

            case 'Project Administrator':            
                if($user->target_project_id == $projectIteration->project_id)
                {
                    return true;
                    break;
                }

            case 'Project Viewer':
                if($user->target_project_id == $projectIteration->project_id)
                {
                    return false;
                    break;
                }

            case 'Iteration Administrator':
                if($user->target_iteration_id == $projectIteration->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Viewer':
                if($user->target_iteration_id == $projectIteration->id)
                {
                    return false;
                    break;
                }
                        
            default:
                return false;
                break;
        }
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, ProjectIteration $projectIteration): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':            
                return true;
                break;

            case 'Client Administrator':            
                if($user->client == $projectIteration->project->client)
                {
                    return true;
                    break;
                }

            case 'Project Administrator':            
                if($user->target_project_id == $projectIteration->project_id)
                {
                    return true;
                    break;
                }

            case 'Project Viewer':
                if($user->target_project_id == $projectIteration->project_id)
                {
                    return false;
                    break;
                }

            case 'Iteration Administrator':
                if($user->target_iteration_id == $projectIteration->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Viewer':
                if($user->target_iteration_id == $projectIteration->id)
                {
                    return false;
                    break;
                }
                        
            default:
                return false;
                break;
        }
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, ProjectIteration $projectIteration): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':            
                return true;
                break;

            case 'Client Administrator':            
                if($user->client == $projectIteration->project->client)
                {
                    return true;
                    break;
                }

            case 'Project Administrator':            
                if($user->target_project_id == $projectIteration->project_id)
                {
                    return false;
                    break;
                }

            case 'Project Viewer':
                if($user->target_project_id == $projectIteration->project_id)
                {
                    return false;
                    break;
                }

            case 'Iteration Administrator':
                if($user->target_iteration_id == $projectIteration->id)
                {
                    return false;
                    break;
                }

            case 'Iteration Viewer':
                if($user->target_iteration_id == $projectIteration->id)
                {
                    return false;
                    break;
                }
                        
            default:
                return false;
                break;
        }
    }

    /**
     * Determine whether the user can detach an ClientModel the model.
     */
    public function detachClientModel(User $user, ProjectIteration $projectIteration): bool
    {
        switch ($user->role->name) 
        {
            case 'Global Administrator':            
                return true;
                break;

            case 'Client Administrator':            
                if($user->client == $projectIteration->project->client)
                {
                    return true;
                    break;
                }

            case 'Project Administrator':            
                if($user->target_project_id == $projectIteration->project_id)
                {
                    return true;
                    break;
                }

            case 'Project Viewer':
                return false;
                break;


            case 'Iteration Administrator':
                if($user->target_iteration_id == $projectIteration->id)
                {
                    return true;
                    break;
                }

            case 'Iteration Viewer':
                return false;
                break;

                        
            default:
                return false;
                break;
        }
    }

    public function replicate(User $user, ProjectIteration $clientModel): bool
    {
        return false;
    }
}
