<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

use Illuminate\Database\Eloquent\Casts\Attribute;

use Illuminate\Support\Facades\Storage;

class Client extends Model
{
    use HasFactory;

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['absolute_logo_path'];
    
    protected function absoluteLogoPath(): Attribute
    {
        if($this->logo_path != null)
        {
            return new Attribute(
                get: fn () => Storage::url($this->logo_path),
            );
        }
        else
        {
            return new Attribute(
                get: fn () => $this->logo_path,
            );
        }
    }

    public function users(): Has<PERSON>any
    {
        return $this->Has<PERSON>any(User::class);
    }
    
    public function projects(): HasMany
    {
        return $this->HasMany(Project::class);
    }

    public function clientModels(): <PERSON><PERSON>any
    {
        return $this->HasMany(ClientModel::class);
    }
}