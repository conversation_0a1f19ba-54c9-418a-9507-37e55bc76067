<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

use MatanYadaev\EloquentSpatial\Traits\HasSpatial;
use App\Traits\HasObfuscatedId;

class Map extends Model
{
    use HasFactory;
    use HasObfuscatedId;

    public function projectIteration(): BelongsTo
    {
        return $this->BelongsTo(ProjectIteration::class);
    }

    public function mapPoints(): HasMany
    {
        return $this->HasMany(MapPoint::class);
    }

    public function mapAreas(): HasMany
    {
        return $this-><PERSON><PERSON>any(MapArea::class);
    }

    public function mapMultiAreas(): HasMany
    {
        return $this->HasMany(MapMultiArea::class);
    }
}
