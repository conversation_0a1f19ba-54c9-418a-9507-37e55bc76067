<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

use MatanYadaev\EloquentSpatial\Traits\HasSpatial;
use MatanYadaev\EloquentSpatial\Objects\Point;

class MapPoint extends Model
{
    use HasFactory;
    use HasSpatial;

    protected $casts = [
        'location' => Point::class,
    ];

    public function map(): BelongsTo
    {
        return $this->BelongsTo(Map::class);
    }
}
