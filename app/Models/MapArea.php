<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

use MatanYadaev\EloquentSpatial\Traits\HasSpatial;
use MatanYadaev\EloquentSpatial\Objects\Polygon;

class MapArea extends Model
{
    use HasFactory;
    use HasSpatial;

    protected $casts = [
        'area'          => Polygon::class,
        'fill_opacity'  => 'float',
        'stroke_width'  => 'integer',
        'stroke_opacity' => 'float',
    ];

    protected $fillable = [
        'name',
        'description',
        'area',
        'map_id',
        'fill_color',
        'fill_opacity',
        'stroke_color',
        'stroke_width',
        'stroke_opacity',
        'dash_pattern'
    ];

    public function map(): BelongsTo
    {
        return $this->BelongsTo(Map::class);
    }
}
