<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Facades\Storage;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

class ClientModel extends Model
{
    use HasFactory;

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['absolute_model_path', 'absolute_thumbnail_path'];
    
    protected function absoluteModelPath(): Attribute
    {
        return new Attribute(
            get: fn () => Storage::url($this->model_path),
        );
    }

    protected function absoluteThumbnailPath(): Attribute
    {
        if($this->thumbnail_path != null)
        {
            return new Attribute(
                get: fn () => Storage::url($this->thumbnail_path),
            );
        }
        else
        {
            return new Attribute(
                get: fn () => $this->thumbnail_path,
            );
        }
    }

    public function client(): BelongsTo
    {
        return $this->BelongsTo(Client::class);
    }
    
    public function projectIterations(): morphToMany
    {
        return $this->morphToMany(ProjectIteration::class, 'iteration_asset')
            ->withPivot
            (
                'id',
                'placement_label',
                'placement_comment',                    
                'latitude',
                'longitude',
                'altitude',
                'vertical_offset',
                'orientation',
                'gradient',
                'group',
                'group_sequence'
            )->using(IterationAsset::class);
    }
}
