<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

use MatanYadaev\EloquentSpatial\Traits\HasSpatial;
use MatanYadaev\EloquentSpatial\Objects\Point;

class VantagePoint extends Model
{
    use HasFactory;
    use HasSpatial;

    protected $casts = [
        'location' => Point::class,
    ];

    protected $fillable = [
        'name',
        'description',
        'project_iteration_id', // Required, for iteration-level vantage points
        'altitude',
        'heading', // Direction the vantage point is facing
        'pitch',   // Up/down angle
        'roll',    // Tilt angle
        'field_of_view', // For camera-like vantage points
    ];

    // Relationship to ProjectIteration
    public function projectIteration(): BelongsTo
    {
        return $this->belongsTo(ProjectIteration::class);
    }
}
