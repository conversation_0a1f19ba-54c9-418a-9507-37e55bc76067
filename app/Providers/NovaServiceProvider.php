<?php

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use <PERSON>vel\Nova\Nova;
use <PERSON>vel\Nova\NovaApplicationServiceProvider;
use Illuminate\Support\Facades\Blade;

use Illuminate\Http\Request;
use Laravel\Nova\Menu\Menu;
use Laravel\Nova\Menu\MenuItem;

use SimonHamp\LaravelNovaCsvImport\LaravelNovaCsvImport;

class NovaServiceProvider extends NovaApplicationServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        // Register custom CSS and scripts
        Nova::serving(function () {
            Nova::style('nova-custom', asset('css/nova-custom.css'));
            Nova::script('map-debug', asset('js/map-debug.js'));
            Nova::script('nova-map-field-fix', asset('js/nova-map-field-fix.js'));
        });

        Nova::userMenu(function (Request $request, Menu $menu) {
            $menu->prepend(
                MenuItem::make(
                    $request->user()->client->name,
                    "/resources/clients/{$request->user()->client->id}"
                )
            );

            return $menu;
        });

        Nova::footer(function ($request) {
            return Blade::render('
                @env(\'prod\')
                    This is production!
                @endenv
                Lovingly Crafted by <a target="_blank" href="https://vizspatial.com">VizAR</a>
                <br>
                <a target="_blank" href=mailto:<EMAIL>>Need help?</a>
            ');
        });
    }

    /**
     * Register the Nova routes.
     *
     * @return void
     */
    protected function routes()
    {
        Nova::routes()
                ->withAuthenticationRoutes()
                ->withPasswordResetRoutes()
                ->register();
    }

    /**
     * Register the Nova gate.
     *
     * This gate determines who can access Nova in non-local environments.
     *
     * @return void
     */
    protected function gate()
    {
        Gate::define('viewNova', function ($user) {
            return true;
        });
    }

    /**
     * Get the dashboards that should be listed in the Nova sidebar.
     *
     * @return array
     */
    protected function dashboards()
    {
        return [
            new \App\Nova\Dashboards\Main,
        ];
    }

    /**
     * Get the tools that should be listed in the Nova sidebar.
     *
     * @return array
     */
    public function tools()
    {
        return [
            (new LaravelNovaCsvImport)->canSee(function ($request) {
                return $request->user()->role->name == 'Global Administrator';
            }),
        ];
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }
}
