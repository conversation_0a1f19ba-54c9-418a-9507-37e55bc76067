<?php

namespace App\Http\Controllers;

use App\Models\IterationAsset;
use Illuminate\Http\Request;

class IterationAssetController extends Controller
{
    public function updateVerticalOffset(Request $request, $id)
    {
        $iterationAsset = IterationAsset::findOrFail($id);

        $this->authorize('updateVerticalOffset', $iterationAsset);

        $request->validate([
            'vertical_offset' => 'required|numeric|min:-9999.99|max:9999.99',
        ]);

        $iterationAsset->vertical_offset = $request->input('vertical_offset');
        $iterationAsset->save();

        return response()->json($iterationAsset);
    }
}