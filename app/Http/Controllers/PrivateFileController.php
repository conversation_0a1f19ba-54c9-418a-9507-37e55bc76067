<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Log;
use App\Models\ProjectIterationFile;
use Illuminate\Support\Facades\Gate;

class PrivateFileController extends Controller
{
    public function show(Request $request, $iteration_id, $filename)
    {
        // Verify user access (e.g., check if the user has permission to access this file)
        if (!$this->hasAccess($iteration_id, $filename)) {
            abort(403, 'Unauthorized');
        }

        // Generate a temporary URL
        $temporaryUrl = URL::temporarySignedRoute(
            'private-files.download',
            now()->addMinutes(15), // URL valid for 60 minutes
            ['iteration_id' => $iteration_id, 'filename' => $filename]
        );

        Log::info("Generated temporary URL for filename: " . $iteration_id . '/' . $filename . 'The following temp URL: ' . $temporaryUrl);

        return redirect($temporaryUrl);
    }

    public function download(Request $request, $iteration_id, $filename)
    {
        // Verify the signature
        if (! $request->hasValidSignature()) {
            abort(401);
        }

        // Construct full path
        $path = $iteration_id . "/". $filename;

        // Log the filename for debugging
        Log::info("Attempting to download filename: " . $path);

        // Check if the file exists
        if (!Storage::disk('iteration_files')->exists($path)) 
        {
            Log::error("File not found: " . $path);
            abort(404);
        }

        // Return the file as a download
        return Storage::disk('iteration_files')->download($path);
    }

    private function hasAccess($iteration_id, $filename)
    {
        Log::info("Checking access to the following iteration & filename: " . $iteration_id . "/" . $filename);
        
        $projectIterationFile = ProjectIterationFile::where('project_iteration_id', $iteration_id)
            ->where('path', $iteration_id . "/" . $filename)
            ->first();

        Log::info("Checking access to the following file: " . $iteration_id . "/" . $filename);

        // Authorize the request using the ProjectIterationFilePolicy
        if (!Gate::allows('view', $projectIterationFile)) 
        {
            Log::warning("Unauthorized access attempt for file: " . $iteration_id . '/' . $filename . " by user: " . auth()->user()->id);
            return false; // User is not authorized to view the file
        }

        return true; // User is authorized to view the file
    }
}