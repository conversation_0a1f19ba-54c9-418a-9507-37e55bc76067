<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Map;

class MapEmbedController extends Controller
{
    /**
     * Show the embed codes for a map.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function showEmbedCodes($id)
    {
        $map = Map::findOrFail($id);

        // Get obfuscated ID
        $obfuscatedId = $map->getObfuscatedId('maps');

        // Generate the embed URL
        $embedUrl = route('embed.view', ['hash' => $obfuscatedId]);

        // Generate the iframe code
        $width = 600;
        $height = 400;
        $iframeCode = "<iframe src=\"{$embedUrl}\" width=\"{$width}\" height=\"{$height}\" style=\"border:0;\" allowfullscreen=\"\" loading=\"lazy\" referrerpolicy=\"no-referrer-when-downgrade\"></iframe>";

        // Generate the JavaScript embed code
        $containerId = "map-container-{$obfuscatedId}";
        $jsCode = "<div id=\"{$containerId}\" style=\"width:{$width}px;height:{$height}px;\"></div>\n";
        $jsCode .= "<script>\n";
        $jsCode .= "  (function() {\n";
        $jsCode .= "    var iframe = document.createElement('iframe');\n";
        $jsCode .= "    iframe.src = '{$embedUrl}';\n";
        $jsCode .= "    iframe.width = '{$width}';\n";
        $jsCode .= "    iframe.height = '{$height}';\n";
        $jsCode .= "    iframe.style.border = '0';\n";
        $jsCode .= "    iframe.allowFullscreen = true;\n";
        $jsCode .= "    iframe.loading = 'lazy';\n";
        $jsCode .= "    iframe.referrerPolicy = 'no-referrer-when-downgrade';\n";
        $jsCode .= "    document.getElementById('{$containerId}').appendChild(iframe);\n";
        $jsCode .= "  })();\n";
        $jsCode .= "</script>";

        // Generate the widget embed code
        $widgetUrl = url("/js/map-embed-widget.js");
        $widgetCode = "<div id=\"vizar-map\" data-map-id=\"{$obfuscatedId}\" data-width=\"{$width}\" data-height=\"{$height}\"></div>\n";
        $widgetCode .= "<script src=\"{$widgetUrl}\"></script>";

        return view('embed.codes', [
            'map' => $map,
            'embedUrl' => $embedUrl,
            'iframeCode' => $iframeCode,
            'jsCode' => $jsCode,
            'widgetCode' => $widgetCode,
        ]);
    }
}
