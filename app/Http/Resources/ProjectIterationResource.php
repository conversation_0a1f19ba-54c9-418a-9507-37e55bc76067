<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use App\Http\Resources\SimpleMapResource;

class ProjectIterationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'model_id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'altitude_source' => $this->altitude_source,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'model_placements' => $this->clientmodels->lazy()->map(function ($clientModel)
            {
                return [
                    'placement_id' => $clientModel->pivot->id,
                    'placement_data' => $this->transformClientModel($clientModel),
                ];
            }),
            'vantage_points' => VantagePointResource::collection($this->vantagepoints),
            'files' => ProjectIterationFileResource::collection($this->projectiterationfiles),
            'maps' => SimpleMapResource::collection($this->maps),
        ];
    }

    /**
     * Transform the client model into a resource.
     *
     * @param  mixed  $clientModel
     * @return ClientModelResource
     */
    private function transformClientModel($clientModel): ClientModelResource
    {
        return new ClientModelResource($clientModel);
    }
}