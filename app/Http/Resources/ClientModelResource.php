<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class ClientModelResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'model_id' => $this->id,
            'model_name' => $this->name,
            'model_description' => $this->description,
            'model_type' => $this->model_type,
            'model_path' => Storage::url($this->model_path),
            'model_thumbnail_path' => $this->when($this->thumbnail_path, Storage::url($this->thumbnail_path), null),
            'model_created_at' => $this->created_at,
            'model_updated_at' => $this->updated_at,
            'placement_label' => $this->pivot->placement_label,
            'placement_comment' => $this->pivot->placement_comment,
            'placement_latitude' => $this->pivot->latitude,
            'placement_longitude' => $this->pivot->longitude,
            'placement_altitude' => $this->pivot->altitude,
            'placement_vertical_offset' => $this->pivot->vertical_offset,
            'placement_orientation' => $this->pivot->orientation,
            'placement_gradient' => $this->pivot->gradient,
            'placement_group' => $this->pivot->group,
            'placement_group_sequence' => $this->pivot->group_sequence,
        ];
    }
}
