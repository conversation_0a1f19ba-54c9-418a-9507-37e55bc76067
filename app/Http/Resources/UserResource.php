<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

use Illuminate\Support\Facades\Auth;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'user_name' => $this->name,
            'user_email' => $this->email,
            'client' => new ClientResource($this->client),
            'role' => $this->role->name,
            'accessible_projects' => $this->client->projects->filter(function ($item)
            {
                return Auth::user()->can('view', $item);
            }
        ),  
        ];
    }
}