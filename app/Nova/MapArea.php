<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

// Fields
use Laravel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\Color;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Panel;
use Mostafaznv\NovaMapField\Fields\MapPolygonField;

// Relationship
use Laravel\Nova\Fields\BelongsTo;

class MapArea extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\MapArea>
     */
    public static $model = \App\Models\MapArea::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'name',
        'description',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            BelongsTo::make('Map')->onlyOnDetail()->rules('required'),
            Text::make('Name')->rules('required', 'max:255')->sortable(),
            Text::make('Description')->rules('required', 'max:255')->hideFromIndex(),
            MapPolygonField::make('Area')
                ->hideDetailButton(false)
                ->withFullScreenControl()
                ->hideFromIndex()
                ->required()
                ->requiredOnCreate()
                ->requiredOnUpdate(),

            Panel::make('Styling Options', [
                // Fill options
                Color::make('Fill Color')
                    ->default('#3388ff')
                    ->hideFromIndex(),

                Number::make('Fill Opacity')
                    ->min(0)
                    ->max(1)
                    ->step(0.1)
                    ->default(0.4)
                    ->hideFromIndex(),

                // Stroke options
                Color::make('Stroke Color')
                    ->default('#3388ff')
                    ->hideFromIndex(),

                Number::make('Stroke Width')
                    ->min(1)
                    ->max(10)
                    ->step(1)
                    ->default(2)
                    ->hideFromIndex(),

                Number::make('Stroke Opacity')
                    ->min(0)
                    ->max(1)
                    ->step(0.1)
                    ->default(0.7)
                    ->hideFromIndex(),

                // Line style options
                Select::make('Line Style', 'dash_pattern')
                    ->options([
                        '' => 'Solid',
                        '5,5' => 'Short Dash',
                        '10,10' => 'Medium Dash',
                        '15,10' => 'Long Dash',
                        '20,10,5,10' => 'Dash-Dot'
                    ])
                    ->displayUsingLabels()
                    ->hideFromIndex(),
            ])->collapsable(),

            DateTime::make('Date Created', 'created_at')->onlyOnDetail(),
            DateTime::make('Date Modified', 'updated_at')->hideWhenUpdating()->hideWhenCreating()->showOnIndex()->sortable(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
