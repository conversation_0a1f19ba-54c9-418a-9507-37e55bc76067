<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Textarea;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\Image;
use <PERSON><PERSON>\Nova\Fields\File;
use <PERSON>vel\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Hidden;

use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\MorphToMany;

class ClientModel extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\ClientModel>
     */
    public static $model = \App\Models\ClientModel::class;

    /**
     * The logical group associated with the resource.
     *
     * @var string
     * 
     * public static $group = 'Model Management'
     */

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = ['name', 'description'];

    /**
     * The number of resources to show per page via relationships.
     *
     * @var int
     */
    public static $perPageViaRelationship = 30;

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        $fieldArray = [
            ID::make()->onlyOnDetail(),
            Image::make('Thumbnail', 'thumbnail_path')->squared()->disableDownload()->showWhenPeeking(),
            Text::make('Model Name', 'name')->showWhenPeeking()->rules('required'),            
            Textarea::make('Description', 'description')->alwaysShow()->showWhenPeeking(),
            File::make('Model File', 'model_path')
                ->disk('public')
                ->storeAs(function (Request $request) {
                     return $request->model_path->getClientOriginalName();
                })
                ->prunable()
                ->deletable(false)
                ->creationRules('required', 'file')
                ->updateRules('sometimes', 'file'),
            
            Select::make('Model Type', 'model_type')
                ->options([
                    'General' => 'General',
                    'Solar Panel' => 'Solar Panel',
                    'Transmission Tower 33kV – Parallel' => 'Transmission Tower 33kV – Parallel',
                    'Transmission Tower 33kV – Single' => 'Transmission Tower 33kV – Single',
                    'Transmission Tower 500kV' => 'Transmission Tower 500kV',
                    'Transmission Tower 220kV' => 'Transmission Tower 220kV',
                    'Wind Turbine' => 'Wind Turbine'])
                ->rules('required'), 

            DateTime::make('Date Created', 'created_at')->sortable()->onlyOnIndex()->showOnDetail(),
            DateTime::make('Date Modified', 'updated_at')->onlyOnDetail()->filterable(),                    
        ];

        if($request->user()->role->name == 'Global Administrator')
            $fieldArray[] = BelongsTo::make('Client')->filterable();
        else
            $fieldArray[] = Hidden::make('client_id')->default($request->user()->client->id);

        return $fieldArray;
    }

    public static function indexQuery(NovaRequest $request, $query)
    {
        if($request->user()->role->name == 'Global Administrator')
            return $query;
        else
            return $query->where('client_id', $request->user()->client->id);
    }

    public static function relatableQuery(NovaRequest $request, $query)
    {
        if($request->user()->role->name == 'Global Administrator')
            return $query;
        else
            return $query->where('client_id', $request->user()->client->id);
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
