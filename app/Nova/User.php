<?php

namespace App\Nova;

use Illuminate\Http\Request;
use Illuminate\Log\Logger;
use Illuminate\Database\Eloquent\Builder;

use Illuminate\Validation\Rules;
use Laravel\Nova\Fields\UiAvatar;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Password;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\FormData;

use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\HasOne;

use Laravel\Nova\Http\Requests\NovaRequest;
use Symfony\Component\CssSelector\Parser\Shortcut\ElementParser;

use Illuminate\Support\Facades\Log;
use <PERSON>vel\Nova\Fields\Hidden;
use <PERSON>vel\Nova\Notifications\NovaChannel;
use Laravel\Nova\Nova;

class User extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\User>
     */
    public static $model = \App\Models\User::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'name', 'email',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        $fieldArray = [
            ID::make()->sortable()->hideFromIndex(),

            UiAvatar::make(),

            Text::make('Name')
                ->sortable()
                ->rules('required', 'max:255'),

            Text::make('Email')
                ->sortable()
                ->rules('required', 'email', 'max:254')
                ->creationRules('unique:users,email')
                ->updateRules('unique:users,email,{{resourceId}}'),

            Password::make('Password')
                ->onlyOnForms()
                ->creationRules('required', Rules\Password::defaults())
                ->updateRules('nullable', Rules\Password::defaults()),
                
            BelongsTo::make('Role', 'role', Role::class)->filterable()->sortable(),
            
            BelongsTo::make('Project', 'project', Project::class)
                ->nullable()
                ->onlyOnForms()
                ->hide()
                ->dependsOn('role', function (BelongsTo $field, NovaRequest $request, FormData $formData) 
                {
                    if ($formData->role == 3 OR $formData->role == 4 OR $formData->role == 5 OR $formData->role == 6)  
                    {
                        $field->show();
                    }
                }),
                   
            BelongsTo::make('Project Iteration', 'projectIteration', ProjectIteration::class)
                ->nullable()
                ->onlyOnForms()
                ->dependsOn(['project', 'role'], function (BelongsTo $field, NovaRequest $request, FormData $formData) 
                {
                    if ($formData->project === null OR ($formData->role != 5 AND $formData->role != 6))
                        $field->hide();
                    
                    $field->relatableQueryUsing(function (NovaRequest $request, Builder $query) use ($formData)
                    {
                        $query->where('project_id', $formData->project);
                    });
                }),   
        ];

        if($request->user()->role->name == 'Global Administrator')
            $fieldArray[] = BelongsTo::make('Client')->filterable();
        else
            $fieldArray[] = Hidden::make('client_id')->default($request->user()->client->id);

        return $fieldArray;
    }

    public static function indexQuery(NovaRequest $request, $query)
    {        
        switch ($request->user()->role->name) 
        {
            case 'Global Administrator':
                # all access
                break;

            case 'Client Administrator':
                $query->where('client_id', $request->user()->client_id);
                $query->where('role_id', '!=', '1');
                break;

            case 'Project Administrator':
                $query->where('client_id', $request->user()->client_id);
                $query->where('role_id', '!=', '1');
                $query->where('role_id', '!=', '2');
                break;
            
            default:
                $query->where('name', "==", "Dimitry is the best");
                break;
        }
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}