<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

// Fields
use Laravel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\DateTime;
use Mostafaznv\NovaMapField\Fields\MapPointField;

// Relationship
use Laravel\Nova\Fields\BelongsTo;

class MapPoint extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\MapPoint>
     */
    public static $model = \App\Models\MapPoint::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'name',
        'description',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            BelongsTo::make('Map')->onlyOnDetail()->rules('required'),
            Text::make('Name')->rules('required', 'max:255')->sortable(),
            Text::make('Description')->rules('required', 'max:255')->hideFromIndex(),
            MapPointField::make('Location')
                ->hideDetailButton(false)
                ->markerIcon(2)
                ->withFullScreenControl()
                ->hideFromIndex()
                ->required()
                ->requiredOnCreate()
                ->requiredOnUpdate(),
            DateTime::make('Date Created', 'created_at')->onlyOnDetail(),
            DateTime::make('Date Modified', 'updated_at')->hideWhenUpdating()->hideWhenCreating()->showOnIndex()->sortable(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
