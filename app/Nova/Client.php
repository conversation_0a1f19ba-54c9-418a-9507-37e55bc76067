<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

use <PERSON><PERSON>\Nova\Panel;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Country;
use <PERSON><PERSON>\Nova\Fields\Avatar;

use <PERSON><PERSON>\Nova\Fields\HasMany;

class Client extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Client>
     */
    public static $model = \App\Models\Client::class;

    /**
     * The logical group associated with the resource.
     *
     * @var string
     * 
     * public static $group = 'Account Management';
     */
     
    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable()->hideFromIndex(),
            Avatar::make('Logo', 'logo_path')->squared()->disableDownload(),
            Text::make('Client Name', 'name')->sortable(), 
            new Panel('Address Information', $this->addressFields()),
        ];
    }
    
    /**
     * Get the address fields for the resource.
     *
     * @return array
     */
    protected function addressFields()
    {
        return [
            Text::make('Address', 'address')->hideFromIndex(),
            Text::make('Suburb', 'suburb')->hideFromIndex(),
            Text::make('State', 'state')->hideFromIndex(),
            Text::make('Postal Code', 'postcode')->hideFromIndex(),
            Country::make('Country', 'country')->hideFromIndex(),
            HasMany::make('Users'),
            HasMany::make('Projects'),            
            HasMany::make('Client Models'),
        ];
    }

    /**
     * Build a "relatable" query for the given resource.
     *
     * This query determines which instances of the model may be attached to other resources.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  \Laravel\Nova\Fields\Field  $field
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function relatableQuery(NovaRequest $request, $query)
    {
        if($request->user()->role->name != 'Global Administrator')
            return $query->where('id', $request->user()->client_id);
        else
            return $query;
    }


    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
    public function menu(Request $request)
    {
        return parent::menu($request)->withBadge(function () {
            return static::$model::count();
        });
    }
}
