<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

use <PERSON>vel\Nova\Fields\HasMany;

use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\File;
use Laravel\Nova\Fields\Hidden;

use Laravel\Nova\Actions\Action;

use Illuminate\Support\Facades\Storage;

class ProjectIterationFile extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\ProjectIterationFile>
     */
    public static $model = \App\Models\ProjectIterationFile::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'name',
        'description',
    ];

    /**
     * Indicates if the resource should be displayed in the sidebar.
     *
     * @var bool
     */
    public static $displayInNavigation = false;

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Text::make('Name')->sortable()->showWhenPeeking()->required(),            
            Textarea::make('Description', 'description')->showWhenPeeking()->alwaysShow()->rows(3)->required(),
            DateTime::make('Date Created', 'created_at')->onlyOnDetail(),
            DateTime::make('Date Modified', 'updated_at')->hideWhenUpdating()->hideWhenCreating()->showOnIndex()->sortable(),           
            File::make('Filename', 'path')
                ->disk('iteration_files')
                ->required()
                ->deletable(false)
                ->prunable()
                ->path($request->viaResourceId ? $request->viaResourceId : $this->resource->project_iteration_id)
                ->storeAs(function (Request $request) 
                {
                    return $request->path->getClientOriginalName();
                }),

            Hidden::make('User', 'user')->default(function ($request) 
            {
                return $request->user()->name;
            }),
        ];
    }

    public function authorizedToReplicate(Request $request)
    {
        return false;
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
