<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Textarea;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\HasMany;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Resource;
use Laravel\Nova\Panel;

use Vizar\CompiledMapCard\CompiledMapCard;
use App\Nova\Actions\GenerateMapEmbedCode;

class Map extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Map>
     */
    public static $model = \App\Models\Map::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'name',
        'description',
    ];

    public static $displayInNavigation = true;

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        $fields = [
            Panel::make('Map Details', [
                Text::make('Map Name', 'name'),
                Textarea::make('Description', 'description')->alwaysShow()->showWhenPeeking(),
                DateTime::make('Date Created', 'created_at')->onlyOnDetail(),
                DateTime::make('Date Modified', 'updated_at')->hideWhenUpdating()->hideWhenCreating()->showOnIndex()->sortable(),
                BelongsTo::make('Project Iteration'),
            ])->limit(2),

            HasMany::make('Map Points'),
            HasMany::make('Map Areas'),
            // HasMany::make('Map Multi Area')
        ];

        return $fields;
    }

    /**
     * Get the cards available on the entity detail view.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [
            (new CompiledMapCard)
                ->onlyOnDetail()
                ->width('full'),
        ];
    }
    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            (new GenerateMapEmbedCode)
                ->onlyOnDetail()
                ->confirmButtonText('Generate Embed Code')
                ->cancelButtonText('Cancel'),
        ];
    }
}
