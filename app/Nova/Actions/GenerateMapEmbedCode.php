<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class GenerateMapEmbedCode extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        $model = $models->first();

        // Get obfuscated ID
        $obfuscatedId = $model->getObfuscatedId('maps');

        // Generate the embed URL
        $embedUrl = url("/embed/{$obfuscatedId}");

        // Generate the iframe code
        $width = $fields->width;
        $height = $fields->height;
        $iframeCode = "<iframe src=\"{$embedUrl}\" width=\"{$width}\" height=\"{$height}\" style=\"border:0;\" allowfullscreen=\"\" loading=\"lazy\" referrerpolicy=\"no-referrer-when-downgrade\"></iframe>";

        // Generate the JavaScript embed code
        $containerId = "map-container-{$obfuscatedId}";
        $jsCode = "<div id=\"{$containerId}\" style=\"width:{$width}px;height:{$height}px;\"></div>\n";
        $jsCode .= "<script>\n";
        $jsCode .= "  (function() {\n";
        $jsCode .= "    var iframe = document.createElement('iframe');\n";
        $jsCode .= "    iframe.src = '{$embedUrl}';\n";
        $jsCode .= "    iframe.width = '{$width}';\n";
        $jsCode .= "    iframe.height = '{$height}';\n";
        $jsCode .= "    iframe.style.border = '0';\n";
        $jsCode .= "    iframe.allowFullscreen = true;\n";
        $jsCode .= "    iframe.loading = 'lazy';\n";
        $jsCode .= "    iframe.referrerPolicy = 'no-referrer-when-downgrade';\n";
        $jsCode .= "    document.getElementById('{$containerId}').appendChild(iframe);\n";
        $jsCode .= "  })();\n";
        $jsCode .= "</script>";

        // Generate the widget embed code
        $widgetUrl = url("/js/map-embed-widget.js");
        $widgetCode = "<div id=\"vizar-map\" data-map-id=\"{$obfuscatedId}\" data-width=\"{$width}\" data-height=\"{$height}\"></div>\n";
        $widgetCode .= "<script src=\"{$widgetUrl}\"></script>";

        // Redirect to the embed codes page
        return Action::openInNewTab(route('maps.embed-codes', ['id' => $model->id]));
    }

    /**
     * Get the fields available on the action.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Number::make('Width', 'width')
                ->default(600)
                ->rules('required', 'numeric', 'min:200', 'max:2000')
                ->help('Width of the embedded map in pixels'),

            Number::make('Height', 'height')
                ->default(400)
                ->rules('required', 'numeric', 'min:200', 'max:2000')
                ->help('Height of the embedded map in pixels'),
        ];
    }
}
