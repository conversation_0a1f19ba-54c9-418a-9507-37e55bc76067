<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ExportModelPlacementsToKml extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        $projectIteration = $models->first();

        // Get all model placements for this project iteration
        $modelPlacements = $projectIteration->clientModels;

        if ($modelPlacements->isEmpty()) {
            return Action::danger('No model placements found for this project iteration.');
        }

        // Generate KML content
        $kmlContent = $this->generateKml($projectIteration, $modelPlacements);

        // Create a temporary file
        $filename = Str::slug($projectIteration->name) . '-model-placements.kml';
        $path = 'kml-exports/' . $filename;

        // Store the file
        Storage::disk('public')->put($path, $kmlContent);

        // Generate download URL
        $url = Storage::disk('public')->url($path);

        // Log the file path for debugging
        Log::info("KML file created at: {$path}");

        return Action::download($url, $filename);
    }

    /**
     * Generate KML content from model placements
     *
     * @param  \App\Models\ProjectIteration  $projectIteration
     * @param  \Illuminate\Support\Collection  $modelPlacements
     * @return string
     */
    protected function generateKml($projectIteration, $modelPlacements)
    {
        // KML header
        $kml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
        $kml .= '<kml xmlns="http://www.opengis.net/kml/2.2">' . PHP_EOL;
        $kml .= '<Document>' . PHP_EOL;
        $kml .= '  <name>' . htmlspecialchars($projectIteration->name) . ' Model Placements</name>' . PHP_EOL;
        $kml .= '  <description>' . htmlspecialchars($projectIteration->description ?? '') . '</description>' . PHP_EOL;

        // Add styles for placemarks
        $kml .= '  <Style id="modelPlacementStyle">' . PHP_EOL;
        $kml .= '    <IconStyle>' . PHP_EOL;
        $kml .= '      <Icon>' . PHP_EOL;
        $kml .= '        <href>http://maps.google.com/mapfiles/kml/paddle/red-circle.png</href>' . PHP_EOL;
        $kml .= '      </Icon>' . PHP_EOL;
        $kml .= '    </IconStyle>' . PHP_EOL;
        $kml .= '  </Style>' . PHP_EOL;

        // Create a folder for the model placements
        $kml .= '  <Folder>' . PHP_EOL;
        $kml .= '    <name>Model Placements</name>' . PHP_EOL;

        // Add each model placement as a placemark
        foreach ($modelPlacements as $model) {
            $pivot = $model->pivot;

            // Skip if no coordinates
            if (empty($pivot->latitude) || empty($pivot->longitude)) {
                continue;
            }

            $name = $pivot->placement_label ?? $model->name ?? 'Unnamed Model';
            $description = $pivot->placement_comment ?? $model->description ?? '';

            $kml .= '    <Placemark>' . PHP_EOL;
            $kml .= '      <name>' . htmlspecialchars($model->name ?? '') . '</name>' . PHP_EOL;
            $kml .= '      <description>' . htmlspecialchars($description) . '</description>' . PHP_EOL;
            $kml .= '      <styleUrl>#modelPlacementStyle</styleUrl>' . PHP_EOL;

            // Add extended data for additional attributes
            $kml .= '      <ExtendedData>' . PHP_EOL;

            if (!empty($pivot->altitude)) {
                $kml .= '        <Data name="altitude">' . PHP_EOL;
                $kml .= '          <value>' . htmlspecialchars($pivot->altitude) . '</value>' . PHP_EOL;
                $kml .= '        </Data>' . PHP_EOL;
            }

            if (!empty($pivot->vertical_offset)) {
                $kml .= '        <Data name="vertical_offset">' . PHP_EOL;
                $kml .= '          <value>' . htmlspecialchars($pivot->vertical_offset) . '</value>' . PHP_EOL;
                $kml .= '        </Data>' . PHP_EOL;
            }

            if (!empty($pivot->orientation)) {
                $kml .= '        <Data name="orientation">' . PHP_EOL;
                $kml .= '          <value>' . htmlspecialchars($pivot->orientation) . '</value>' . PHP_EOL;
                $kml .= '        </Data>' . PHP_EOL;
            }

            if (!empty($pivot->gradient)) {
                $kml .= '        <Data name="gradient">' . PHP_EOL;
                $kml .= '          <value>' . htmlspecialchars($pivot->gradient) . '</value>' . PHP_EOL;
                $kml .= '        </Data>' . PHP_EOL;
            }

            if (!empty($pivot->group)) {
                $kml .= '        <Data name="group">' . PHP_EOL;
                $kml .= '          <value>' . htmlspecialchars($pivot->group) . '</value>' . PHP_EOL;
                $kml .= '        </Data>' . PHP_EOL;
            }

            if (!empty($pivot->group_sequence)) {
                $kml .= '        <Data name="group_sequence">' . PHP_EOL;
                $kml .= '          <value>' . htmlspecialchars($pivot->group_sequence) . '</value>' . PHP_EOL;
                $kml .= '        </Data>' . PHP_EOL;
            }

            $kml .= '      </ExtendedData>' . PHP_EOL;

            // Add point coordinates
            $altitude = $pivot->altitude ?? 0;
            $kml .= '      <Point>' . PHP_EOL;
            $kml .= '        <altitudeMode>relativeToGround</altitudeMode>' . PHP_EOL;
            $kml .= '        <coordinates>' . $pivot->longitude . ',' . $pivot->latitude . ',' . $altitude . '</coordinates>' . PHP_EOL;
            $kml .= '      </Point>' . PHP_EOL;
            $kml .= '    </Placemark>' . PHP_EOL;
        }

        // Close the KML structure
        $kml .= '  </Folder>' . PHP_EOL;
        $kml .= '</Document>' . PHP_EOL;
        $kml .= '</kml>';

        return $kml;
    }

    /**
     * Get the fields available on the action.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [];
    }
}
