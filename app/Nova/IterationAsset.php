<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Select;


class IterationAsset extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\IterationAsset>
     */
    public static $model = \App\Models\IterationAsset::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'placement_label'
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make(),
            Text::make('Placement Label')->required(),
            Text::make('Placement Comment'),
            Number::make('project_iteration_id')->required(),
            Number::make('iteration_asset_id')->required(),
            Select::make('iteration_asset_type')
                ->options([
                    'client' => 'Client',
                    'library' => 'Library',
                ])
                ->required(),
            Number::make('latitude')->required(),
            Number::make('longitude')->required(),
            Number::make('altitude'),
            Number::make('vertical_offset'),
            Number::make('orientation'),
            Number::make('gradient'),
            Number::make('group'),
            Number::make('group_sequence'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
