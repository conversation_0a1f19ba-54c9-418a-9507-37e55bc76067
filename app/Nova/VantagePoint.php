<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Textarea;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use Laravel\Nova\Panel;


use Laravel\Nova\Fields\BelongsTo;

use Mostafaznv\NovaMapField\Fields\MapPointField;

class VantagePoint extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\VantagePoint>
     */
    public static $model = \App\Models\VantagePoint::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'name', 'description',
    ];

    /**
     * Indicates if the resource should be displayed in the sidebar.
     *
     * @var bool
     */
    public static $displayInNavigation = false;

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Panel::make('Vantage Point Details',
            [
                Text::make('Name')
                    ->sortable()
                    ->rules('required', 'max:255'),

                Textarea::make('Description')
                    ->alwaysShow()
                    ->hideFromIndex(),

                MapPointField::make('Location')
                    ->templateUrl('https://{a-c}.tile.openstreetmap.org/{z}/{x}/{y}.png')
                    ->projection('EPSG:3857')
                    ->defaultLatitude(-37.830545203043)
                    ->defaultLongitude(144.97344167754)
                    ->zoom(13)
                    ->srid(3857)
                    ->markerIcon(2)
                    ->rules('required', 'max:255'),

                BelongsTo::make('Project Iteration', 'projectIteration')
                    ->rules('required'),

                DateTime::make('Created At')
                    ->sortable()
                    ->onlyOnDetail(),

                DateTime::make('Updated At')
                    ->sortable()
                    ->onlyOnDetail(),

            ]),

            Panel::make('(Optional) Spatial Configuration',
            [
                Number::make('Altitude')
                    ->step(0.01)
                    ->nullable()
                    ->hideFromIndex()
                    ->help('In: Meters Min: -5,000 Max: 20,000 Precision: 0.01'),

                Number::make('Heading')
                    ->step(0.01)
                    ->nullable()
                    ->hideFromIndex()
                    ->help('In: Degrees Min: 0 Max: 359.99 Precision: 0.01'),

                Number::make('Pitch')
                    ->step(0.01)
                    ->nullable()
                    ->hideFromIndex()
                    ->help('In: Degrees Min: -90 Max: 90 Precision: 0.01'),

                Number::make('Roll')
                    ->step(0.01)
                    ->nullable()
                    ->hideFromIndex()
                    ->help('In: Degrees Min: -180 Max: 180 Precision: 0.01'),

                Number::make('Field of View', 'field_of_view')
                    ->step(0.01)
                    ->nullable()
                    ->hideFromIndex()
                    ->help('In: Degrees Min: 0 Max: 180 Precision: 0.01'),

            ])->collapsedByDefault(),










        ];
    }

    /**
     * Get the Details fields for the resource.
     *
     * @return array
     */
    // protected function detailsFields()
    // {
    //     return [
    //         Text::make('Address', 'address_line_1')->hideFromIndex(),
    //         Text::make('Address Line 2')->hideFromIndex(),
    //         Text::make('City')->hideFromIndex(),
    //         Text::make('State')->hideFromIndex(),
    //         Text::make('Postal Code')->hideFromIndex(),
    //         Country::make('Country')->hideFromIndex(),
    //     ];
    // }

    //     /**
    //  * Get the Details fields for the resource.
    //  *
    //  * @return array
    //  */
    // protected function spatialFields()
    // {
    //     return [

    //     ];
    // }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
