<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="150px" height="46px" viewBox="0 0 150 46" version="1.1">
<defs>
<filter id="alpha" filterUnits="objectBoundingBox" x="0%" y="0%" width="100%" height="100%">
  <feColorMatrix type="matrix" in="SourceGraphic" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
</filter>
<mask id="mask0">
  <g filter="url(#alpha)">
<rect x="0" y="0" width="150" height="46" style="fill:rgb(0%,0%,0%);fill-opacity:0.815686;stroke:none;"/>
  </g>
</mask>
<clipPath id="clip1">
  <rect x="0" y="0" width="150" height="46"/>
</clipPath>
<g id="surface5" clip-path="url(#clip1)">
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(9.411765%,55.686277%,68.627453%);fill-opacity:1;" d="M 49.160156 12.402344 C 49.175781 12.769531 49.015625 13.011719 48.679688 13.125 C 48.660156 12.757812 48.820312 12.515625 49.160156 12.402344 Z M 49.160156 12.402344 "/>
</g>
<mask id="mask1">
  <g filter="url(#alpha)">
<rect x="0" y="0" width="150" height="46" style="fill:rgb(0%,0%,0%);fill-opacity:0.815686;stroke:none;"/>
  </g>
</mask>
<clipPath id="clip2">
  <rect x="0" y="0" width="150" height="46"/>
</clipPath>
<g id="surface8" clip-path="url(#clip2)">
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(9.019608%,54.509807%,67.450982%);fill-opacity:1;" d="M 65.023438 18.425781 C 65.042969 18.792969 64.882812 19.03125 64.542969 19.148438 C 64.527344 18.777344 64.6875 18.539062 65.023438 18.425781 Z M 65.023438 18.425781 "/>
</g>
<mask id="mask2">
  <g filter="url(#alpha)">
<rect x="0" y="0" width="150" height="46" style="fill:rgb(0%,0%,0%);fill-opacity:0.564706;stroke:none;"/>
  </g>
</mask>
<clipPath id="clip3">
  <rect x="0" y="0" width="150" height="46"/>
</clipPath>
<g id="surface11" clip-path="url(#clip3)">
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(8.627451%,55.686277%,68.627453%);fill-opacity:1;" d="M 140.265625 26.613281 C 140.601562 26.726562 140.761719 26.96875 140.746094 27.335938 C 140.40625 27.222656 140.246094 26.980469 140.265625 26.613281 Z M 140.265625 26.613281 "/>
</g>
<mask id="mask3">
  <g filter="url(#alpha)">
<rect x="0" y="0" width="150" height="46" style="fill:rgb(0%,0%,0%);fill-opacity:0.501961;stroke:none;"/>
  </g>
</mask>
<clipPath id="clip4">
  <rect x="0" y="0" width="150" height="46"/>
</clipPath>
<g id="surface14" clip-path="url(#clip4)">
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(9.019608%,59.215689%,72.549021%);fill-opacity:1;" d="M 141.464844 28.539062 C 141.804688 28.652344 141.964844 28.894531 141.945312 29.261719 C 141.609375 29.148438 141.449219 28.90625 141.464844 28.539062 Z M 141.464844 28.539062 "/>
</g>
<mask id="mask4">
  <g filter="url(#alpha)">
<rect x="0" y="0" width="150" height="46" style="fill:rgb(0%,0%,0%);fill-opacity:0.564706;stroke:none;"/>
  </g>
</mask>
<clipPath id="clip5">
  <rect x="0" y="0" width="150" height="46"/>
</clipPath>
<g id="surface17" clip-path="url(#clip5)">
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(8.235294%,56.078434%,67.843139%);fill-opacity:1;" d="M 143.390625 31.671875 C 143.726562 31.785156 143.886719 32.023438 143.871094 32.394531 C 143.53125 32.277344 143.371094 32.039062 143.390625 31.671875 Z M 143.390625 31.671875 "/>
</g>
<mask id="mask5">
  <g filter="url(#alpha)">
<rect x="0" y="0" width="150" height="46" style="fill:rgb(0%,0%,0%);fill-opacity:0.564706;stroke:none;"/>
  </g>
</mask>
<clipPath id="clip6">
  <rect x="0" y="0" width="150" height="46"/>
</clipPath>
<g id="surface20" clip-path="url(#clip6)">
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(8.627451%,56.470591%,69.01961%);fill-opacity:1;" d="M 144.589844 33.597656 C 144.929688 33.710938 145.089844 33.953125 145.070312 34.320312 C 144.734375 34.207031 144.574219 33.964844 144.589844 33.597656 Z M 144.589844 33.597656 "/>
</g>
<mask id="mask6">
  <g filter="url(#alpha)">
<rect x="0" y="0" width="150" height="46" style="fill:rgb(0%,0%,0%);fill-opacity:0.501961;stroke:none;"/>
  </g>
</mask>
<clipPath id="clip7">
  <rect x="0" y="0" width="150" height="46"/>
</clipPath>
<g id="surface23" clip-path="url(#clip7)">
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(9.411765%,59.215689%,72.549021%);fill-opacity:1;" d="M 145.792969 35.523438 C 146.132812 35.636719 146.292969 35.878906 146.273438 36.246094 C 145.9375 36.132812 145.777344 35.890625 145.792969 35.523438 Z M 145.792969 35.523438 "/>
</g>
<mask id="mask7">
  <g filter="url(#alpha)">
<rect x="0" y="0" width="150" height="46" style="fill:rgb(0%,0%,0%);fill-opacity:0.564706;stroke:none;"/>
  </g>
</mask>
<clipPath id="clip8">
  <rect x="0" y="0" width="150" height="46"/>
</clipPath>
<g id="surface26" clip-path="url(#clip8)">
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(8.627451%,56.470591%,69.01961%);fill-opacity:1;" d="M 147.714844 38.65625 C 148.054688 38.769531 148.214844 39.007812 148.195312 39.378906 C 147.859375 39.261719 147.699219 39.023438 147.714844 38.65625 Z M 147.714844 38.65625 "/>
</g>
<mask id="mask8">
  <g filter="url(#alpha)">
<rect x="0" y="0" width="150" height="46" style="fill:rgb(0%,0%,0%);fill-opacity:0.752941;stroke:none;"/>
  </g>
</mask>
<clipPath id="clip9">
  <rect x="0" y="0" width="150" height="46"/>
</clipPath>
<g id="surface29" clip-path="url(#clip9)">
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(3.529412%,40.784314%,47.058824%);fill-opacity:1;" d="M 57.089844 39.617188 C 59.613281 39.855469 62.175781 39.9375 64.785156 39.859375 C 62.179688 40.175781 59.535156 40.175781 56.851562 39.859375 C 56.878906 39.710938 56.960938 39.632812 57.089844 39.617188 Z M 57.089844 39.617188 "/>
</g>
<mask id="mask9">
  <g filter="url(#alpha)">
<rect x="0" y="0" width="150" height="46" style="fill:rgb(0%,0%,0%);fill-opacity:0.501961;stroke:none;"/>
  </g>
</mask>
<clipPath id="clip10">
  <rect x="0" y="0" width="150" height="46"/>
</clipPath>
<g id="surface32" clip-path="url(#clip10)">
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(9.411765%,59.215689%,72.549021%);fill-opacity:1;" d="M 148.917969 40.582031 C 149.257812 40.695312 149.417969 40.9375 149.398438 41.304688 C 149.0625 41.191406 148.902344 40.949219 148.917969 40.582031 Z M 148.917969 40.582031 "/>
</g>
<mask id="mask10">
  <g filter="url(#alpha)">
<rect x="0" y="0" width="150" height="46" style="fill:rgb(0%,0%,0%);fill-opacity:0.627451;stroke:none;"/>
  </g>
</mask>
<clipPath id="clip11">
  <rect x="0" y="0" width="150" height="46"/>
</clipPath>
<g id="surface35" clip-path="url(#clip11)">
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(8.235294%,56.470591%,69.01961%);fill-opacity:1;" d="M 144.351562 45.398438 C 144.976562 45.582031 145.617188 45.664062 146.273438 45.640625 C 146.273438 45.71875 146.273438 45.800781 146.273438 45.878906 C 145.714844 45.878906 145.152344 45.878906 144.589844 45.878906 C 144.511719 45.71875 144.429688 45.558594 144.351562 45.398438 Z M 144.351562 45.398438 "/>
</g>
</defs>
<g id="surface1">
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(3.137255%,32.549021%,38.039216%);fill-opacity:1;" d="M 39.304688 -0.121094 C 40.265625 -0.121094 41.226562 -0.121094 42.1875 -0.121094 C 45.121094 1.4375 45.882812 3.765625 44.472656 6.863281 C 42.632812 8.898438 40.507812 9.261719 38.101562 7.949219 C 36.503906 6.574219 35.984375 4.847656 36.539062 2.769531 C 37.023438 1.359375 37.945312 0.398438 39.304688 -0.121094 Z M 39.304688 -0.121094 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(2.745098%,32.156864%,37.254903%);fill-opacity:1;" d="M -0.121094 3.734375 C -0.121094 3.492188 -0.121094 3.25 -0.121094 3.011719 C 1.503906 0.582031 3.667969 -0.0625 6.371094 1.082031 C 9.800781 12.074219 13.203125 23.070312 16.585938 34.078125 C 19.988281 23.253906 23.355469 12.414062 26.683594 1.566406 C 29.4375 0.160156 31.640625 0.761719 33.292969 3.371094 C 29.167969 16.738281 25.039062 30.105469 20.914062 43.472656 C 19.167969 45.625 16.964844 46.226562 14.304688 45.277344 C 13.558594 44.929688 12.875 44.488281 12.257812 43.953125 C 8.195312 30.496094 4.070312 17.089844 -0.121094 3.734375 Z M -0.121094 3.734375 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(2.745098%,32.156864%,37.64706%);fill-opacity:1;" d="M 112.140625 45.878906 C 110.9375 45.878906 109.734375 45.878906 108.535156 45.878906 C 104.179688 33.148438 99.890625 20.382812 95.671875 7.585938 C 91.441406 20.101562 87.234375 32.625 83.054688 45.15625 C 80.316406 46.367188 78.273438 45.644531 76.921875 42.988281 C 76.761719 42.667969 76.761719 42.347656 76.921875 42.027344 C 81.410156 28.699219 85.898438 15.375 90.382812 2.046875 C 93.265625 0.507812 96.269531 0.226562 99.398438 1.203125 C 100.121094 1.390625 100.722656 1.75 101.203125 2.289062 C 105.84375 15.996094 110.328125 29.722656 114.664062 43.472656 C 114.15625 44.660156 113.316406 45.464844 112.140625 45.878906 Z M 112.140625 45.878906 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(2.745098%,31.764707%,36.862746%);fill-opacity:1;" d="M 140.265625 26.613281 C 140.246094 26.980469 140.40625 27.222656 140.746094 27.335938 C 141.078125 27.671875 141.320312 28.074219 141.464844 28.539062 C 141.449219 28.90625 141.609375 29.148438 141.945312 29.261719 C 142.546875 29.984375 143.027344 30.785156 143.390625 31.671875 C 143.371094 32.039062 143.53125 32.277344 143.871094 32.394531 C 144.203125 32.730469 144.445312 33.132812 144.589844 33.597656 C 144.574219 33.964844 144.734375 34.207031 145.070312 34.320312 C 145.40625 34.65625 145.648438 35.058594 145.792969 35.523438 C 145.777344 35.890625 145.9375 36.132812 146.273438 36.246094 C 146.875 36.96875 147.355469 37.769531 147.714844 38.65625 C 147.699219 39.023438 147.859375 39.261719 148.195312 39.378906 C 148.53125 39.714844 148.773438 40.117188 148.917969 40.582031 C 148.902344 40.949219 149.0625 41.191406 149.398438 41.304688 C 149.558594 41.464844 149.71875 41.625 149.878906 41.785156 C 149.878906 42.027344 149.878906 42.265625 149.878906 42.507812 C 149.203125 44.144531 148.003906 45.191406 146.273438 45.640625 C 145.617188 45.664062 144.976562 45.582031 144.351562 45.398438 C 140.617188 39.324219 136.851562 33.261719 133.054688 27.214844 C 131.296875 26.984375 129.535156 26.824219 127.765625 26.734375 C 127.390625 26.441406 127.113281 26.078125 126.921875 25.648438 C 126.574219 24.171875 126.855469 22.847656 127.765625 21.675781 C 131.0625 21.652344 134.347656 21.492188 137.621094 21.195312 C 138.332031 20.6875 138.773438 20.003906 138.941406 19.148438 C 139.101562 15.773438 139.101562 12.402344 138.941406 9.03125 C 138.59375 8.121094 137.996094 7.441406 137.140625 6.984375 C 132.8125 6.863281 128.484375 6.824219 124.160156 6.863281 C 124.199219 19.386719 124.160156 31.910156 124.039062 44.433594 C 123.179688 45.085938 122.257812 45.566406 121.273438 45.878906 C 120.875 45.878906 120.472656 45.878906 120.070312 45.878906 C 119.003906 45.605469 118.082031 45.042969 117.308594 44.195312 C 117.148438 30.144531 117.148438 16.097656 117.308594 2.046875 C 117.722656 1.539062 118.246094 1.175781 118.871094 0.964844 C 125.921875 0.804688 132.972656 0.804688 140.023438 0.964844 C 143.28125 1.90625 145.324219 4.035156 146.152344 7.34375 C 146.226562 12.25 146.066406 17.148438 145.671875 22.035156 C 145.328125 22.785156 144.886719 23.464844 144.351562 24.082031 C 143.046875 25.058594 141.683594 25.898438 140.265625 26.613281 Z M 140.265625 26.613281 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(3.137255%,32.549021%,38.039216%);fill-opacity:1;" d="M 41.226562 45.878906 C 40.746094 45.878906 40.265625 45.878906 39.785156 45.878906 C 39.066406 45.578125 38.386719 45.175781 37.742188 44.675781 C 37.3125 34.082031 37.152344 23.488281 37.257812 12.886719 C 38.546875 11.011719 40.269531 10.488281 42.429688 11.320312 C 43.0625 11.792969 43.503906 12.394531 43.75 13.125 C 43.9375 22.960938 43.898438 32.796875 43.628906 42.628906 C 43.550781 43.3125 43.46875 43.992188 43.390625 44.675781 C 42.683594 45.148438 41.964844 45.550781 41.226562 45.878906 Z M 41.226562 45.878906 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(2.745098%,32.156864%,37.64706%);fill-opacity:1;" d="M 57.089844 39.617188 C 56.960938 39.632812 56.878906 39.710938 56.851562 39.859375 C 59.535156 40.175781 62.179688 40.175781 64.785156 39.859375 C 67.828125 39.820312 70.875 39.859375 73.917969 39.980469 C 74.484375 40.671875 74.726562 41.472656 74.640625 42.386719 C 74.726562 43.300781 74.484375 44.105469 73.917969 44.796875 C 65.824219 44.957031 57.734375 44.957031 49.640625 44.796875 C 48.523438 44.175781 48.042969 43.210938 48.195312 41.90625 C 48.238281 41.464844 48.277344 41.023438 48.316406 40.582031 C 53.847656 33.507812 59.257812 26.363281 64.542969 19.148438 C 64.882812 19.03125 65.042969 18.792969 65.023438 18.425781 C 65.171875 18.007812 65.414062 17.644531 65.746094 17.339844 C 60.375 17.261719 55.007812 17.179688 49.640625 17.097656 C 48.480469 15.953125 48.160156 14.628906 48.679688 13.125 C 49.015625 13.011719 49.175781 12.769531 49.160156 12.402344 C 49.265625 12.171875 49.425781 11.972656 49.640625 11.800781 C 57.410156 11.640625 65.183594 11.640625 72.957031 11.800781 C 73.476562 12 73.839844 12.363281 74.039062 12.886719 C 74.199219 14.25 74.199219 15.613281 74.039062 16.980469 C 68.421875 24.570312 62.773438 32.117188 57.089844 39.617188 Z M 57.089844 39.617188 "/>
<use xlink:href="#surface5" mask="url(#mask0)"/>
<use xlink:href="#surface8" mask="url(#mask1)"/>
<use xlink:href="#surface11" mask="url(#mask2)"/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(2.745098%,32.549021%,37.64706%);fill-opacity:1;" d="M 94.109375 27.574219 C 97.605469 26.984375 99.609375 28.429688 100.121094 31.910156 C 99.300781 35.933594 96.980469 37.179688 93.148438 35.644531 C 90.785156 33.421875 90.625 31.054688 92.667969 28.539062 C 93.164062 28.210938 93.644531 27.890625 94.109375 27.574219 Z M 94.109375 27.574219 "/>
<use xlink:href="#surface14" mask="url(#mask3)"/>
<use xlink:href="#surface17" mask="url(#mask4)"/>
<use xlink:href="#surface20" mask="url(#mask5)"/>
<use xlink:href="#surface23" mask="url(#mask6)"/>
<use xlink:href="#surface26" mask="url(#mask7)"/>
<use xlink:href="#surface29" mask="url(#mask8)"/>
<use xlink:href="#surface32" mask="url(#mask9)"/>
<use xlink:href="#surface35" mask="url(#mask10)"/>
</g>
</svg>
