<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Map Embed Codes - {{ $map->name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        pre {
            white-space: pre-wrap;
            word-break: break-all;
            background-color: #f3f4f6;
            padding: 1rem;
            border-radius: 0.5rem;
            font-family: monospace;
            font-size: 0.875rem;
            overflow-x: auto;
        }

        .copy-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
        }

        .code-container {
            position: relative;
        }

        .preview-container {
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            overflow: hidden;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto py-8 px-4">
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="mb-6">
                <h1 class="text-2xl font-bold text-gray-800">Map Embed Codes: {{ $map->name }}</h1>
            </div>

            <p class="text-gray-600 mb-6">
                Use these embed codes to include this map on your website or application.
            </p>

            <!-- Direct URL -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-3">Direct URL</h2>
                <p class="text-gray-600 mb-2">
                    Direct link to the embedded map view:
                </p>
                <div class="code-container">
                    <input type="text" value="{{ $embedUrl }}"
                           class="w-full p-3 border border-gray-300 rounded-md font-mono text-sm"
                           onclick="this.select()" readonly>
                    <button class="copy-btn bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-md text-sm"
                            onclick="copyToClipboard(this.previousElementSibling.value, this)">
                        Copy
                    </button>
                </div>
            </div>

            <!-- HTML Embed (iframe) -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-3">HTML Embed (iframe)</h2>
                <p class="text-gray-600 mb-2">
                    Add this code to your HTML to embed the map:
                </p>
                <div class="code-container">
                    <pre id="iframe-code">{{ $iframeCode }}</pre>
                    <button class="copy-btn bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-md text-sm"
                            onclick="copyToClipboard(document.getElementById('iframe-code').textContent, this)">
                        Copy
                    </button>
                </div>
                <div class="mt-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Preview:</h3>
                    <div class="preview-container">
                        {!! $iframeCode !!}
                    </div>
                </div>
            </div>

            <!-- JavaScript Embed -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-3">JavaScript Embed</h2>
                <p class="text-gray-600 mb-2">
                    Use this JavaScript code to dynamically embed the map:
                </p>
                <div class="code-container">
                    <pre id="js-code">{{ $jsCode }}</pre>
                    <button class="copy-btn bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-md text-sm"
                            onclick="copyToClipboard(document.getElementById('js-code').textContent, this)">
                        Copy
                    </button>
                </div>
            </div>

            <!-- Widget Embed -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-3">Widget Embed</h2>
                <p class="text-gray-600 mb-2">
                    Use our widget for a responsive embed with additional features:
                </p>
                <div class="code-container">
                    <pre id="widget-code">{{ $widgetCode }}</pre>
                    <button class="copy-btn bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-md text-sm"
                            onclick="copyToClipboard(document.getElementById('widget-code').textContent, this)">
                        Copy
                    </button>
                </div>
                <p class="text-gray-500 text-sm mt-2">
                    Note: The widget provides a responsive embed with additional features. Include the script once per page.
                </p>
            </div>
        </div>
    </div>

    <script>
        function copyToClipboard(text, button) {
            navigator.clipboard.writeText(text).then(function() {
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.classList.remove('bg-blue-500', 'hover:bg-blue-600');
                button.classList.add('bg-green-500', 'hover:bg-green-600');

                setTimeout(function() {
                    button.textContent = originalText;
                    button.classList.remove('bg-green-500', 'hover:bg-green-600');
                    button.classList.add('bg-blue-500', 'hover:bg-blue-600');
                }, 2000);
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                button.textContent = 'Error!';
                button.classList.remove('bg-blue-500', 'hover:bg-blue-600');
                button.classList.add('bg-red-500', 'hover:bg-red-600');

                setTimeout(function() {
                    button.textContent = 'Copy';
                    button.classList.remove('bg-red-500', 'hover:bg-red-600');
                    button.classList.add('bg-blue-500', 'hover:bg-blue-600');
                }, 2000);
            });
        }
    </script>
</body>
</html>
