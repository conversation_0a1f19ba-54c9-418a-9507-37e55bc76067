<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LibraryModel>
 */
class LibraryModelFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->sentence(2),
            'description' => fake()->realText($maxNbChars = 200, $indexSize = 2),
            'model_path' => fake()->imageUrl(640, 480, 'model', true, '3d Model', true, 'png'),
            'thumbnail_path' => fake()->imageUrl(150, 150, 'thumbnail', true, 'logo', true, 'jpg'),
        ];
    }
}
