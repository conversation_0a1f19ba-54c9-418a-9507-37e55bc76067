<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

use App\Models\Client;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Project>
 */
class ProjectFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->sentence(3),
            'description' => fake()->realText($maxNbChars = 200, $indexSize = 2),
            'type' => 'infrastructure',
            'latitude' => fake()->latitude($min = -45, $max = -40),
            'longitude' => fake()->longitude($min = 140, $max = 145),
        ];
    }
}
