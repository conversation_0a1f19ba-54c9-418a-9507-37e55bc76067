<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Carbon\Carbon;


use App\Models\Client;
use App\Models\Project;
use App\Models\ProjectIteration;
use App\Models\User;
use App\Models\Role;
use App\Models\ClientModel;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create a Global Admin Role
        DB::table('roles')->insert([
            'name' => 'Global Administrator',
            'created_at' => now(),
            'updated_at' => now(),       
        ]);

        Role::factory(1)->create();

        Client::factory(count: 5)
            ->has(Project::factory(count: 3)
                ->has(ProjectIteration::factory(count: 5)
                    ->sequence(
                        ['name' => '1st Iteration', 'altitude_source' => 'Google Earth'],
                        ['name' => '2nd Iteration', 'altitude_source' => 'ArcGIS'],
                        ['name' => '3rd Iteration', 'altitude_source' => 'Google Earth'],
                        ['name' => '4th Iteration', 'altitude_source' => 'ArcGIS'],
                        ['name' => '5th Iteration', 'altitude_source' => 'Google Earth'],
                    )
                    ->hasAttached(ClientModel::factory(count: 5)
                        ->sequence(
                            ['model_path' => 'samples/SolarPanel-DoublePanelTracker.fbx', 'model_type' => 'Solar Panel'],
                            ['model_path' => 'samples/SolarPanel-SinglePanelTracker.fbx', 'model_type' => 'Solar Panel'],
                            ['model_path' => 'samples/BirdOfParadise_Prefab.fbx', 'model_type' => 'General'],
                            ['model_path' => 'samples/Bush1_LOD0_Prefab.fbx', 'model_type' => 'General'],
                            ['model_path' => 'samples/ElectrolyserPlant_500kTris_TestMode_PREFAB.fbx', 'model_type' => 'Transmission Tower 500kV'],
                            ['model_path' => 'samples/Elephant_Ear_Prefab.fbx', 'model_type' => 'General'],
                            ['model_path' => 'samples/SM_GrassTree_01_Prefab.fbx', 'model_type' => 'General'],
                            ['model_path' => 'samples/SM_GrassTree_02_Prefab.fbx', 'model_type' => 'General'],
                            ['model_path' => 'samples/Palm_Bush_Prefab.fbx', 'model_type' => 'General'],
                            ['model_path' => 'samples/33kV_Parallel_OHL_PREFAB.fbx', 'model_type' => 'Transmission Tower 33kV – Parallel'],
                            ['model_path' => 'samples/33kV_Parallel_RightAngle_PREFAB.fbx', 'model_type' => 'Transmission Tower 33kV – Parallel'],
                            ['model_path' => 'samples/33kV_Parallel_Termination_PREFAB.fbx', 'model_type' => 'Transmission Tower 33kV – Parallel'],
                            ['model_path' => 'samples/WindTurbine-260m_PREFAB.fbx', 'model_type' => 'Wind Turbine'],
                            ['model_path' => 'samples/WindTurbine-250m_PREFAB.fbx', 'model_type' => 'Wind Turbine'],
                            ['model_path' => 'samples/RWE_V172_PREFAB.fbx', 'model_type' => 'General'],
                        )
                    )
                )
            )
            ->has(User::factory(count: 4))
            ->create();
        
        // Create an Nova Admin user for Dimitry
        DB::table('users')->insert([
            'name' => 'Dimitry Rybkin',
            'email' => '<EMAIL>',
            'password' => Hash::make('1z2x3c4v'),
            'client_id' => 1,
            'role_id' => 1,
            'created_at' => now(),
            'updated_at' => now(),       
        ]);

        // Create an Nova Admin user for Dimitry
        DB::table('users')->insert([
            'name' => 'Tristan Place',
            'email' => '<EMAIL>',
            'password' => Hash::make('1z2x3c4v'),
            'client_id' => 1,
            'role_id' => 1,
            'created_at' => now(),
            'updated_at' => now(),       
        ]);

        // Create an Nova Admin user for Dimitry
        DB::table('users')->insert([
            'name' => 'Arthur Cheng',
            'email' => '<EMAIL>',
            'password' => Hash::make('1z2x3c4v'),
            'client_id' => 1,
            'role_id' => 1,
            'created_at' => now(),
            'updated_at' => now(),       
        ]);

        // Create an Nova Admin user for Dimitry
        DB::table('users')->insert([
            'name' => 'Wing Cheung',
            'email' => '<EMAIL>',
            'password' => Hash::make('1z2x3c4v'),
            'client_id' => 1,
            'role_id' => 1,
            'created_at' => now(),
            'updated_at' => now(),       
        ]);

        $this->call([
            // ClientSeeder::class,      
            // ProjectSeeder::class,  
            // ProjectIterationSeeder::class,  
        ]); 
    }
}
