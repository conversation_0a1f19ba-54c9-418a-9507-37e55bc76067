<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop Altitude Source from Iteration Assets
        Schema::table('iteration_assets', function (Blueprint $table) 
        {
            $table->dropColumn('altitude_source');
        });

        // Add the Altitude Source to Porjet Iterations
        Schema::table('project_iterations', function (Blueprint $table) 
        {
            $table->enum('altitude_source', ['Google Earth', 'ArcGIS'])->nullable($value = true);
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop Altitude Source from Project Iteration
        Schema::table('project_iterations', function (Blueprint $table) 
        {
            $table->dropColumn('altitude_source');
        });

        // Re-add Altitude Source to Iteration Assets
        Schema::table('iteration_assets', function (Blueprint $table) 
        {
            $table->enum('altitude_source', ['Google Earth', 'ArcGIS'])->nullable($value = true);
        });
    }
};
