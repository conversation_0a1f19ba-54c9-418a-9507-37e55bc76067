<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('iteration_assets', function (Blueprint $table) 
        {
            $table->enum('altitude_source', ['Google Earth', 'ArcGIS'])->nullable($value = true);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('iteration_assets', function (Blueprint $table) 
        {
            $table->dropColumn('altitude_source');
        });
    }
};
