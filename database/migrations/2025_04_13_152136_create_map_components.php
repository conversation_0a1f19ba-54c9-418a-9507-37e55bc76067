<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('map_points', function (Blueprint $table)
        {
            $table->id();
            $table->foreignId('map_id')->constrained();
            $table->string('name', length: 255);
            $table->string('description', length: 255)->nullable();
            $table->point('location');
            $table->timestamps();
        });

        Schema::create('map_areas', function (Blueprint $table)
        {
            $table->id();
            $table->foreignId('map_id')->constrained();
            $table->string('name', length: 255);
            $table->string('description', length: 255)->nullable();
            $table->polygon('area');
            $table->timestamps();
        });

        Schema::create('map_multi_areas', function (Blueprint $table)
        {
            $table->id();
            $table->foreignId('map_id')->constrained();
            $table->string('name', length: 255);
            $table->string('description', length: 255)->nullable();
            $table->multiPolygon('areas');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('map_points');
        Schema::dropIfExists('map_areas');
        Schema::dropIfExists('map_multi_areas');
    }
};
