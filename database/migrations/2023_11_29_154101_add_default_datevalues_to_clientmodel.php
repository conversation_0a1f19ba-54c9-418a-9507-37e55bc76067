<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('client_models', function (Blueprint $table) 
        {
            // Add datetime population on creation & on update
            $table->dateTime('created_at')->useCurrent()->change();
            $table->dateTime('updated_at')->useCurrentOnUpdate()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('client_models', function (Blueprint $table) 
        {
            // Revert migration
            $table->dateTime('created_at')->change();
            $table->dateTime('updated_at')->change();
        });
    }
};
