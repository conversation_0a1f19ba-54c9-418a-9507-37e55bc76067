<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('map_areas', function (Blueprint $table) {
            $table->string('fill_color')->nullable()->default('#3388ff');
            $table->float('fill_opacity')->nullable()->default(0.4);
            $table->string('stroke_color')->nullable()->default('#3388ff');
            $table->integer('stroke_width')->nullable()->default(2);
            $table->float('stroke_opacity')->nullable()->default(0.7);
            $table->string('dash_pattern')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('map_areas', function (Blueprint $table) {
            $table->dropColumn([
                'fill_color',
                'fill_opacity',
                'stroke_color',
                'stroke_width',
                'stroke_opacity',
                'dash_pattern'
            ]);
        });
    }
};
