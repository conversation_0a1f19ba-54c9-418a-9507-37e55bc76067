<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('client_models', function (Blueprint $table) 
        {
            $table->enum('model_type', [
                'General',
                'Solar Panel',
                'Transmission Tower 33kV – Parallel',
                'Transmission Tower 33kV – Single',
                'Transmission Tower 500kV',
                'Wind Turbine'
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('client_models', function (Blueprint $table) 
        {
            $table->dropColumn('model_type');
        });
    }
};
