<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('iteration_assets', function (Blueprint $table) 
        {
            $table->renameColumn('model_id', 'iteration_asset_id');
            $table->renameColumn('model_type', 'iteration_asset_type');
            $table->renameColumn('iteration_id', 'project_iteration_id');

            $table->dropForeign('fk_iteration_assets_library_models_1');           
        });

        Schema::table('client_models', function (Blueprint $table) 
        {
            $table->dropForeign('fk_models_clients_1');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('iteration_assets', function (Blueprint $table) 
        {
            $table->renameColumn('iteration_asset_id', 'model_id');
            $table->renameColumn('iteration_asset_type', 'model_type');
            $table->renameColumn('project_iteration_id', 'iteration_id');

            //@todo: finish the rollback
            $table->foreign('')->references('id')->on('library_models');
        });
    }
};
