<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vantage_points', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->unsignedInteger('project_iteration_id');
            $table->foreign('project_iteration_id')->references('id')->on('project_iterations')->onDelete('cascade');
            $table->point('location');
            $table->decimal('altitude', 10, 2)->nullable();
            $table->decimal('heading', 10, 2)->nullable(); // 0-360 degrees
            $table->decimal('pitch', 10, 2)->nullable();   // -90 to 90 degrees
            $table->decimal('roll', 10, 2)->nullable();    // -180 to 180 degrees
            $table->decimal('field_of_view', 5, 2)->nullable(); // Field of view in degrees
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vantage_points');
    }
};
