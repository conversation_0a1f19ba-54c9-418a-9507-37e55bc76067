<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_iteration_files', function (Blueprint $table) 
        {
            $table->id();
            $table->unsignedInteger('project_iteration_id');
            $table->foreign('project_iteration_id')->references('id')->on('project_iterations');
            $table->string('name', length: 255);
            $table->string('description', length: 255);
            $table->string('path', length: 255);
            $table->string('user', length: 100);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_iteration_files');
    }
};
