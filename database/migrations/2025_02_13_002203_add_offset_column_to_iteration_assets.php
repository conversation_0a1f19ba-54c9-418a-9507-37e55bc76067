<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('iteration_assets', function (Blueprint $table) 
        {
            $table
                ->decimal('vertical_offset', total: 6, places: 2)
                ->nullable($value = true)
                ->after('altitude');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('iteration_assets', function (Blueprint $table) 
        {
            $table->dropColumn('vertical_offset');
        });
    }
};
