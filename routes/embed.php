<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Models\Map;

/*
|--------------------------------------------------------------------------
| Embed Routes
|--------------------------------------------------------------------------
|
| These routes are specifically for the embeddable map feature.
| They are public routes that don't require authentication.
|
*/

// Public API endpoint to get map data for embedding
Route::get('/embed/map/{hash}', function (Request $request, $hash) {
    try {
        $map = Map::findByObfuscatedIdOrFail($hash, 'maps');

        // Basic map information
        $mapData = [
            'id' => $map->getObfuscatedId('maps'), // Return obfuscated ID
            'name' => $map->name,
            'description' => $map->description,
        ];

        // Get map points
        $mapData['points'] = $map->mapPoints->map(function ($point) {
            return [
                'id' => $point->id,
                'name' => $point->name,
                'description' => $point->description,
                'location' => json_encode($point->location),
            ];
        });

        // Get map areas
        $mapData['areas'] = $map->mapAreas->map(function ($area) {
            return [
                'id' => $area->id,
                'name' => $area->name,
                'description' => $area->description,
                'area' => json_encode($area->area),
                'style' => [
                    'fillColor' => $area->fill_color ?? '#3388ff',
                    'fillOpacity' => $area->fill_opacity ?? 0.4,
                    'color' => $area->stroke_color ?? '#3388ff',
                    'weight' => $area->stroke_width ?? 2,
                    'opacity' => $area->stroke_opacity ?? 0.7,
                    'dashArray' => $area->dash_pattern,
                ],
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $mapData,
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
        ], 500);
    }
})->name('embed.map');

// Embed view for the map
Route::get('/embed/{hash}', function (Request $request, $hash) {
    try {
        $map = Map::findByObfuscatedIdOrFail($hash, 'maps');
        return view('embed.map', ['map' => $map]);
    } catch (\Exception $e) {
        return response()->view('embed.error', ['error' => $e->getMessage()], 500);
    }
})->name('embed.view');
