<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::redirect('/', 'nova/dashboards/main');

Route::middleware('auth:sanctum')->get('/private-files/{iteration_id}/{filename}', [App\Http\Controllers\PrivateFileController::class, 'show'])->name('private-files.show');
Route::middleware('auth:sanctum')->get('/private-files/download/{iteration_id}/{filename}', [App\Http\Controllers\PrivateFileController::class, 'download'])->name('private-files.download')->middleware('signed');

// Map embed codes page
Route::middleware('auth:sanctum')->get('/maps/{id}/embed-codes', [App\Http\Controllers\MapEmbedController::class, 'showEmbedCodes'])->name('maps.embed-codes');
