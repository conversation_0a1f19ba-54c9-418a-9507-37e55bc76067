/**
 * VizAR Map Embed Widget
 *
 * This widget allows embedding VizAR maps on any website.
 *
 * Usage:
 * <div id="vizar-map" data-map-id="obfuscatedMapId" data-width="600" data-height="400"></div>
 * <script src="https://your-domain.com/js/map-embed-widget.js"></script>
 */
(function() {
    // Configuration
    const config = {
        baseUrl: window.location.origin,
        defaultWidth: 600,
        defaultHeight: 400
    };

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        // Find all map containers
        const containers = document.querySelectorAll('[id^="vizar-map"]');

        containers.forEach(function(container) {
            // Get map ID and dimensions
            const mapId = container.getAttribute('data-map-id');
            const width = container.getAttribute('data-width') || config.defaultWidth;
            const height = container.getAttribute('data-height') || config.defaultHeight;

            if (!mapId) {
                console.error('VizAR Map Widget: Missing data-map-id attribute');
                container.innerHTML = '<div style="color: red; padding: 10px;">Error: Missing map ID</div>';
                return;
            }

            // Set container dimensions
            if (width.toString().includes('%')) {
                container.style.width = width;
            } else {
                container.style.width = `${width}px`;
            }

            if (height.toString().includes('%')) {
                container.style.height = height;
            } else {
                container.style.height = `${height}px`;
            }

            container.style.position = 'relative';

            // Create iframe
            const iframe = document.createElement('iframe');
            iframe.src = `${config.baseUrl}/embed/${mapId}`;
            iframe.style.width = '100%';
            iframe.style.height = '100%';
            iframe.style.border = '0';
            iframe.style.display = 'block'; // Prevents extra space below iframe
            iframe.allowFullscreen = true;
            iframe.loading = 'lazy';
            iframe.referrerPolicy = 'no-referrer-when-downgrade';

            // Add iframe to container
            container.appendChild(iframe);

            // Add attribution if not disabled
            if (container.getAttribute('data-hide-attribution') !== 'true') {
                const attribution = document.createElement('div');
                attribution.style.position = 'absolute';
                attribution.style.bottom = '0';
                attribution.style.right = '0';
                attribution.style.background = 'rgba(255, 255, 255, 0.7)';
                attribution.style.padding = '2px 5px';
                attribution.style.fontSize = '10px';
                attribution.style.borderTopLeftRadius = '3px';
                attribution.innerHTML = 'Powered by <a href="https://vizspatial.com" target="_blank" style="color: #0078A8; text-decoration: none;">VizAR</a>';

                container.appendChild(attribution);
            }
        });
    });
})();
